<template>
  <div class="q-ml-md" v-bind="$attrs">
    <!-- รายการตัวเลือก (Radio Input หน้า Text Field) -->
    <div
      v-for="(_option, index) in store.radioOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        @mouseover="store.hoverRow(index)"
        style="cursor: move"
        :data-cy="'drag-button-' + index"
      />

      <q-radio
        v-model="store.checkboxOptions"
        :val="store.radioOptions[index]?.value"
        color="primary"
        disable
        class="q-mr-sm"
      />

      <div class="row items-center">
        <q-input
          v-model="store.radioOptions[index]!.optionText"
          :placeholder="store.radioOptions[index]!.placeholder"
          dense
          @update:model-value="() => handleOptionTextChange(index)"
          @blur="handleOptionTextBlur(index)"
          class="q-mr-sm"
          :data-cy="'option-text-input-' + index"
        />

        <!-- <q-input
          v-if="props.type === 'quiz'"
          v-model.number="store.radioOptions[index]!.score"
          placeholder="คะแนน"
          type="number"
          dense
          style="max-width: 80px"
          @input="handleOptionScoreChange(index)"
          @blur="handleOptionScoreBlur(index, $event)"
        /> -->
      </div>

      <q-btn
        flat
        round
        icon="image"
        color="grey"
        class="q-ml-sm"
        @click="handleImageUpload(index)"
      />

      <q-btn
        v-if="store.radioOptions.length > 1"
        flat
        round
        icon="close"
        @click="handleDeleteOption(index)"
        :disable="store.radioOptions.length <= 1"
        class="q-ml-sm"
      />

      <!-- Display uploaded image for this option -->
      <div v-if="getOptionImagePath(index)" class="q-ml-xl q-mt-sm">
        <img
          :src="getOptionImagePath(index)!"
          alt="Option image"
          style="
            width: 200px;
            height: 200px;
            object-fit: contain;
            border: 1px solid #ddd;
            border-radius: 4px;
          "
        />
      </div>
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn
        flat
        color="secondary"
        label="เพิ่มตัวเลือก"
        icon="add"
        @click="handleAddOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
      <q-btn
        v-if="!route.path.includes('quiz')"
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="handleAddOtherOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
    </div>
  </div>

  <!-- Upload Image Dialog -->
  <UploadImage
    v-model="showImageDialog"
    :item-block-id="props.itemBlock.id"
    :option-id="selectedOptionId"
    @image-uploaded="handleImageUploaded"
  />
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import UploadImage from 'src/components/common/UploadImage.vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock, Option } from 'src/types/models';
import { OptionService, type CreateOptionData } from 'src/services/asm/optionService';
import { useRoute } from 'vue-router';

const route = useRoute();

// Define interface for option update data
interface OptionUpdateData {
  index: number;
  option: Option;
  action?: string;
}

defineOptions({
  inheritAttrs: false,
});

// Inject the store from the parent component
const store = inject<ItemBlockStore>('blockStore');

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'value',
    value: string | number,
  ) => void;
  triggerOptionImageSave: (optionId: number, imagePath: string) => void;
  handleOptionImageUploaded: (optionId: number, imagePath: string) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Image upload state
const showImageDialog = ref(false);
const selectedOptionId = ref<number | null>(null);

// Make sure the store is available
if (!store) {
  console.error('ItemBlockStore not provided to ChoiceAns component');
  throw new Error('ItemBlockStore not provided to ChoiceAns component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Define emits for updating parent component
const emit = defineEmits<{
  'update:options': [options: Option[]];
  'option:created': [option: Option];
  'option:updated': [optionId: number, data: OptionUpdateData];
}>();

// Initialize option service
const optionService = new OptionService();

// Loading state for option creation
const isCreatingOption = ref(false);

// Utility function to extract original relative path from potentially transformed URLs
function extractOriginalImagePath(imagePath: string | null | undefined): string | null {
  if (!imagePath) return null;

  // If it's already a relative path (uploaded_files/filename.ext), return as is
  if (
    imagePath.startsWith('uploaded_files/') &&
    !imagePath.includes('?') &&
    !imagePath.includes('http')
  ) {
    return imagePath;
  }

  // If it's a signed URL, extract the relative path part
  if (imagePath.includes('uploaded_files/')) {
    const match = imagePath.match(/uploaded_files\/[^?]+/);
    return match ? match[0] : null;
  }

  return imagePath;
}

// Handler for option text changes (real-time updates without saving)
const handleOptionTextChange = (index: number) => {
  // Update the store immediately for UI responsiveness
  store.updateOption(index);
  // No auto-save here - save only on blur
};

// Handler for option text blur (save on blur)
const handleOptionTextBlur = async (index: number) => {
  const storeOption = store.radioOptions[index];
  const optionText = storeOption?.optionText || '';

  // If store option doesn't have an ID yet, it needs to be created first
  if (storeOption && !storeOption.id) {
    // Only create if user has entered some text
    if (optionText.trim()) {
      try {
        const optionData: CreateOptionData = {
          optionText: optionText,
          itemBlockId: props.itemBlock.id,
          value: storeOption.score || 0,
        };

        const createdOption = await optionService.createOption(optionData);

        // Update the store with the backend-generated ID
        store.updateOptionId(index, createdOption.id);

        console.log('✅ [RADIO-BLUR] Option created and store updated:', {
          index,
          optionId: createdOption.id,
          optionText: createdOption.optionText,
          storeOptionId: store.radioOptions[index]?.id,
          itemBlockId: props.itemBlock.id,
        });

        // Emit option update to parent component
        emit('option:updated', createdOption.id, {
          index,
          option: createdOption,
        });
      } catch (error) {
        console.error('❌ Failed to create option on blur:', error);
      }
    }
  } else if (storeOption && storeOption.id && autoSave) {
    // Option exists, trigger save via parent component
    autoSave.triggerOptionAutoSave(storeOption.id, 'optionText', optionText);

    // ✅ FIX: Find the actual option from props to preserve imagePath and other backend data
    const actualOption = props.itemBlock.options?.find((opt) => opt.id === storeOption.id);

    // ✅ CRITICAL FIX: Extract original relative path to prevent signed URL storage
    const originalImagePath = extractOriginalImagePath(actualOption?.imagePath);

    emit('option:updated', storeOption.id, {
      index,
      option: {
        ...storeOption,
        optionText,
        itemBlockId: props.itemBlock.id,
        id: storeOption.id,
        value: Number(storeOption.score || 0),
        // ✅ CRITICAL: Use original relative path format to prevent signed URL storage
        ...(originalImagePath && { imagePath: originalImagePath }),
        ...(actualOption?.sequence !== undefined && { sequence: actualOption.sequence }),
        ...(actualOption?.nextSection !== undefined &&
          actualOption.nextSection !== null && { nextSection: actualOption.nextSection }),
      },
    });
  }
};

// // Handler for option score changes (real-time updates without saving)
// const handleOptionScoreChange = (index: number) => {
//   // Update the store for real-time UI updates
//   store.updateOption(index);
//   // No auto-save here - save only on blur
// };

// // Handler for option score blur (save on blur)
// const handleOptionScoreBlur = async (index: number, event: Event) => {
//   const target = event.target as HTMLInputElement;
//   const newValue = Number(target.value) || 0;
//   const storeOption = store.radioOptions[index];

//   console.log('🔍 [RADIO] Option score blur - checking option state:', {
//     index,
//     storeOptionId: storeOption?.id,
//     newValue,
//     itemBlockOptionsLength: props.itemBlock.options?.length || 0,
//   });

//   if (storeOption && storeOption.id && autoSave) {
//     // For score changes, we need to update the value field
//     console.log('💾 [RADIO] Triggering auto-save for option score:', {
//       optionId: storeOption.id,
//       newValue,
//     });
//     autoSave.triggerOptionAutoSave(storeOption.id, 'value', newValue);
//   } else if (storeOption && storeOption.id) {
//     // Fallback: use optionService directly if auto-save is not available
//     try {
//       console.log('🔄 [RADIO] Direct update for option score:', {
//         optionId: storeOption.id,
//         newValue,
//       });
//       await optionService.updateOption(storeOption.id, {
//         optionText: storeOption.optionText,
//         itemBlockId: props.itemBlock.id,
//         value: newValue,
//       });
//     } catch (error) {
//       console.error('Failed to update option score:', error);
//     }
//   }
// };

// Handler for deleting an option
const handleDeleteOption = async (index: number) => {
  try {
    // Get the option from the store (not from props)
    const storeOption = store.radioOptions[index];

    // If option has an ID, delete it from backend first
    if (storeOption && storeOption.id) {
      await optionService.removeOption(storeOption.id);
    }

    // Remove from local store
    store.removeOption(index);
  } catch (error) {
    console.error('❌ Failed to delete option:', error);
  }
};

// Handler for adding new option
const handleAddOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOption();

    // Prepare option data for API
    const newOptionIndex = store.radioOptions.length - 1;
    const newOption = store.radioOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data including the ID
      if (store.radioOptions[newOptionIndex]) {
        store.radioOptions[newOptionIndex].id = createdOption.id;
        store.radioOptions[newOptionIndex].optionText = createdOption.optionText;
        store.radioOptions[newOptionIndex].score = createdOption.value;
      }

      console.log('✅ [RADIO-ADD] Option created via Add button:', {
        optionId: createdOption.id,
        optionText: createdOption.optionText,
        itemBlockId: createdOption.itemBlockId,
        storeOptionId: store.radioOptions[newOptionIndex]?.id,
        newOptionIndex,
      });

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }
  } catch (error) {
    console.error('❌ [RADIO] Failed to create option:', error);
    console.error('❌ [RADIO] Context during failure:', {
      itemBlockId: props.itemBlock.id,
      assessmentId: props.itemBlock.assessmentId,
      optionsCount: store.radioOptions.length,
    });

    // Remove the option from store if API call failed
    if (store.radioOptions.length > 1) {
      store.removeOption(store.radioOptions.length - 1);
    }
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for adding "other" option
const handleAddOtherOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // Add to local store for immediate UI feedback
    store.radioOptions.push({
      placeholder: 'อื่นๆ',
      value: `other${store.radioOptions.length + 1}`,
      optionText: 'อื่นๆ', // Set to 'อื่นๆ' for backend
      score: 0,
      sequence: store.radioOptions.length + 1,
    });

    // Prepare option data for API
    const newOptionIndex = store.radioOptions.length - 1;
    const newOption = store.radioOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create other option in store');
    }

    const optionData: CreateOptionData = {
      optionText: 'อื่นๆ', // Always send 'อื่นๆ' to backend
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      if (store.radioOptions[newOptionIndex]) {
        store.radioOptions[newOptionIndex].id = createdOption.id;
        store.radioOptions[newOptionIndex].optionText = createdOption.optionText;
        store.radioOptions[newOptionIndex].score = createdOption.value;
      }
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }
  } catch (error) {
    console.error('❌ [RADIO-OTHER] Failed to create "other" option:', error);
    if (store.radioOptions.length > 1) {
      store.removeOption(store.radioOptions.length - 1);
    }
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for image upload button click
const handleImageUpload = (index: number) => {
  const option = store.radioOptions[index];
  if (option && option.id) {
    selectedOptionId.value = option.id;
    showImageDialog.value = true;
  } else {
    console.warn('Cannot upload image: option has no ID yet');
  }
};

// Handler for image uploaded event
const handleImageUploaded = () => {
  if (selectedOptionId.value) {
    console.log(
      '🎯 [OPTION-IMAGE] Image uploaded successfully for option:',
      selectedOptionId.value,
    );

    // Close the dialog and reset state
    showImageDialog.value = false;
    const uploadedOptionId = selectedOptionId.value;
    selectedOptionId.value = null;

    // ✅ ADD DELAY to ensure backend processing is complete before refresh
    setTimeout(() => {
      console.log('🔄 [OPTION-IMAGE] Triggering assessment refresh after image upload');
      // Emit refresh to parent to reload assessment data and show the uploaded image
      emit('option:updated', uploadedOptionId, {
        index: -1,
        option: {} as Option,
        action: 'image-uploaded-refresh',
      });
    }, 500); // 500ms delay to ensure backend processing is complete
  }
};

// Get option image path for display
const getOptionImagePath = (index: number) => {
  const option = props.itemBlock.options?.find((opt) => opt.id === store.radioOptions[index]?.id);
  return option?.imagePath || null;
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}

.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}
</style>
