<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAuthStore } from 'src/stores/auth';

import { QIcon, QInput, QBtn, QForm } from 'quasar';
import { useRouter } from 'vue-router';
import MainFooter from 'src/components/MainFooter.vue';

const router = useRouter();
const visiblePassword = ref(false);
const authStore = useAuthStore();

const form = ref<QForm | null>(null);
const rememberMe = ref(false);

const login = async () => {
  const valid = await form.value?.validate();
  if (valid) {
    const success = await authStore.loginBuu();
    if (success) {
      // Check if there's a redirect path stored
      const redirectPath = localStorage.getItem('redirectAfterLogin');
      if (redirectPath && redirectPath !== '/login') {
        localStorage.removeItem('redirectAfterLogin');
        await router.replace(redirectPath);
      } else {
        await router.replace({ name: 'home' });
      }
    }
  }
};

onMounted(() => {
  // Reset all auth state and clear form fields when login page loads
  authStore.resetState();
  // Reset form validation state
  form.value?.resetValidation();
});
</script>

<template>
  <q-page class="q-pa-none" style="min-height: 100vh; position: relative; overflow: hidden">
    <div
      style="
        position: fixed;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(to bottom, var(--q-primary) 0%, #fff 100%);
        z-index: 1;
      "
    ></div>
    <div
      class="flex justify-center items-center"
      style="
        position: fixed;
        left: 0;
        top: 0;
        width: 60vw;
        height: 100vh;
        z-index: 2;
        pointer-events: none;
      "
    >
      <q-img src="/brand/BUU-HRD.svg" style="width: 700px; height: 700px; object-fit: contain" />
    </div>

    <div
      style="
        position: absolute;
        top: 0;
        right: 0;
        width: 40vw;
        min-width: 340px;
        height: 100vh;
        background: #fff;
        box-shadow: -8px 0 32px 0 rgba(31, 38, 135, 0.1);
        border-radius: 80px 0 0 0;
        z-index: 3;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      "
    >
      <div style="width: 100%; max-width: 400px; margin: 0 auto">
        <q-form ref="form" @submit="login()" class="q-gutter-y-lg">
          <div class="flex flex-center">
            <q-img
              src="/svg/burapha.svg"
              style="width: 150px; height: 150px; object-fit: contain"
            />
          </div>
          <div class="text-center text-h5">มหาวิทยาลัยบูรพา</div>
          <div class="flex flex-center q-mb-md">
            <q-icon name="lock" color="primary" class="q-mr-sm" />
            <span class="text-h6 text-bold">เข้าสู่ระบบ HRD</span>
          </div>
          <div style="font-weight: 500; margin-bottom: 2px; line-height: 1">อีเมล/รหัสพนักงาน</div>
          <q-input
            style="margin-top: 6px"
            color="accent"
            dense
            outlined
            data-cy="login_username"
            label="ชื่อผู้ใช้"
            v-model="authStore.loginUsername"
            :error="authStore.incorrectUsernamePasswordStatus"
            :error-message="
              authStore.incorrectUsernamePasswordStatus
                ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                : undefined
            "
            :rules="[(val) => !!val || 'กรุณากรอกชื่อผู้ใช้']"
            @keyup.enter="login"
          >
            <template v-slot:prepend>
              <q-icon name="account_circle" color="grey-6"></q-icon>
            </template>
          </q-input>
          <div style="font-weight: 500; margin-top: 8px; margin-bottom: 2px">รหัสผ่าน</div>
          <q-input
            style="margin-top: 2px"
            color="accent"
            :type="visiblePassword ? 'text' : 'password'"
            dense
            outlined
            data-cy="login_password"
            label="รหัสผ่าน"
            v-model="authStore.loginPassword"
            :error="authStore.incorrectUsernamePasswordStatus"
            :error-message="
              authStore.incorrectUsernamePasswordStatus
                ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                : undefined
            "
            :rules="[(val) => !!val || 'กรุณากรอกรหัสผ่าน']"
            @keyup.enter="login"
          >
            <template v-slot:prepend>
              <q-icon name="key" color="grey-6"></q-icon>
            </template>
            <template v-slot:append>
              <q-icon
                :data-cy="visiblePassword ? 'i-eye' : 'i-eyeOff'"
                :name="visiblePassword ? 'visibility' : 'visibility_off'"
                color="grey-4"
                @click="visiblePassword = !visiblePassword"
                class="cursor-pointer"
              ></q-icon>
            </template>
          </q-input>
          <q-checkbox v-model="rememberMe" label="จำฉันไว้ในระบบ" class="q-mt-xs" size="xs" />
          <q-btn
            type="submit"
            unelevated
            dense
            class="text-black full-width text-body1"
            data-cy="login_btn"
            label="เข้าสู่ระบบ"
            color="primary"
          >
          </q-btn>
        </q-form>
        <div class="q-mt-lg flex justify-center">
          <a href="https://myid.buu.ac.th/" class="text-accent q-mt-sm font-weight-regular"
            >ลืมรหัสผ่าน ?</a
          >
        </div>
      </div>
    </div>
    <MainFooter />
  </q-page>
</template>

<style>
html,
body,
#q-app {
  overflow: hidden !important;
  height: 100%;
}
</style>
